# Google firebase auth 流程及注意事项

目前支持的认证方式有:

- Google
- Email password
- Email link

> 相关 REST API 文档
> https://cloud.google.com/identity-platform/docs/use-rest-api?hl=zh-cn
> https://cloud.google.com/identity-platform/docs/reference/rest/v1/accounts?hl=zh-cn

## Google 服务代理: `/api/googleapis`

- rest: 代理端点
- 自动添加 firebase api key
- 其他与 googleapis 文档一致

## Email link 认证流程

> ⚠️ 注意: “电子邮件链接登录”电子邮件限额 5 封电子邮件/天

1. 用户点击登录按钮, 发送登录链接到邮箱 
    ⚠️ 正常情况下可由客户端调用 firebase sdk 直接发送链接, 但因为特殊地区网络的原因, 客户端无法请求 Google 服务, 因此此步骤因由服务端完成.
    - [发送链接 RESTAPI](https://identitytoolkit.googleapis.com/v1/accounts:sendOobCode)
    - [Doc](https://cloud.google.com/identity-platform/docs/reference/rest/v1/accounts/sendOobCode?hl=zh-cn)
    - Body:
       ```json
       {
            "requestType": "EMAIL_SIGNIN",
            "email": "<EMAIL>",
            "clientType": "CLIENT_TYPE_WEB",
            "continueUrl": "<callback_url>",
            "dynamicLinkDomain": "webtools.page.link",
            "canHandleCodeInApp": true,
            "iOSBundleId": "com.example.webtools",
            "androidInstallApp": true,
            "androidMinimumVersionCode": "12",
            "androidPackageName": "com.example.webtools",
          }
      ```

2. 用户点击邮箱链接, 跳转到回调页面
    ⚠️ 正常情况下可使用 firebase 提供的默认操作地址, [可查看 Auth 模板页面](https://console.firebase.google.com/u/0/project/your-project/authentication/emails)来完成回调页面的开发. 但部分地区无法访问 firebase, 因此需要自定义回调页面, 由服务端来完成. 相关设置在Auth 模板页面(模板-编辑-自定义操作网址).
    > 已设置自定义操作网址: /__/auth/action
    > 网址参数: ?mode=<mode>&oobCode=<oobCode>&apiKey=<apiKey>&continueUrl=<continueUrl>&lang=<lang>

3. 回调页面验证链接, 完成登录
    ⚠️ 正常情况下可由客户端调用 firebase sdk 直接验证链接, 但因为特殊地区网络的原因, 客户端无法请求 Google 服务, 因此此步骤因由服务端完成.
    - [emailLinkSignin RESTAPI](https://identitytoolkit.googleapis.com/v1/accounts:emailLinkSignin)
    - [Doc](https://cloud.google.com/identity-platform/docs/reference/rest/v1/accounts/signInWithEmailLink)

4. 登录成功, 跳转到 `continueUrl`

## Email password 注册及邮箱验证流程

1.  用户在注册页面输入邮箱和密码，点击注册按钮。
2.  客户端调用 `accounts:signUp` REST API 创建用户。
3.  如果注册成功，客户端会收到 `idToken`，然后调用 `accounts:sendOobCode` REST API 发送验证邮件。
4.  用户点击邮件中的验证链接，跳转到回调页面 (`/__/auth/action`)。
5.  回调页面调用 `accounts:update` REST API，并使用 `oobCode` 来验证用户的邮箱。

## Email password 登录流程

1.  用户在登录页面输入邮箱和密码，点击登录按钮。
2.  客户端调用 `accounts:signInWithPassword` REST API 登录用户。
3.  如果登录成功，客户端会收到 `idToken` 和 `refreshToken`，并将它们保存起来用于后续的请求。

## Email password 忘记密码/重设密码流程

1.  用户在登录页面点击“忘记密码”链接。
2.  客户端调用 `accounts:sendOobCode` REST API，并设置 `requestType` 为 `PASSWORD_RESET`，发送密码重置邮件。
3.  用户点击邮件中的重置链接，跳转到回调页面 (`/__/auth/action`)。
4.  回调页面首先调用 `accounts:resetPassword` REST API 验证 `oobCode`。
5.  验证通过后，提示用户输入新密码。
6.  用户输入新密码后，客户端再次调用 `accounts:resetPassword` REST API，并附带 `oobCode` 和新密码来完成密码重置。

[相关 REST API Doc](https://cloud.google.com/identity-platform/docs/use-rest-api?hl=zh-cn)

## Google 认证流程

1. 由于 Google 认证需要打开 Google 页面, 因此该这流程可使用 Firebase 默认的认证流程, 无需更改.
2. 认证成功后, 需要和服务端同步信息, 比如激活码等.

> 无法访问 Google 服务的用户, 不显示 Google 登录选项.

⚠️ 由于支持特殊地区的认证流程, 因此全平台统一采用 JWT 登录认证方式, 此部分需要服务端支持. 此部分见 **JWT 更新**部分.

## JWT 更新

Firebase auth 服务, JWT 有效期固定为 1小时, 因此客户端需要记录 JWT 刷新时间, 在重新刷新页面时判断是否需要刷新令牌, 并且最少要每一小时请求一次刷新令牌, 以及在重新登录时更新 JWT.

1. 客户端检查是否需要刷新令牌, 如果需要
2. 向服务端请求 Firebase 刷新令牌 ( 使用服务端 googleapis 代理 API 服务: `/api/googleapis`)
3. 服务端返回刷新后的 JWT 给客户端
4. 客户端更新 JWT, 并定时 59分钟后请求一次刷新令牌

[Firebase 刷新令牌 API Doc](https://cloud.google.com/identity-platform/docs/use-rest-api?hl=zh-cn#section-refresh-token)

## 激活码流程

用户成功注册/首次登录后, 第二步需要进行激活码认证, 并跳转到主页.

1. 成功注册/首次登录, 开始下一步激活码认证
2. 进入第二步骤, 输入激活码, 点击提交, 向服务端请求激活码认证
3. 服务端验证激活码, 并返回结果
4. 客户端根据结果跳转到相应页面
5. 激活成功跳转到主页
6. 激活失败, 则显示激活失败提示.