<script lang="ts">
  import { browser } from "$app/environment";
  import type { Toast, ToastType } from "$lib/components/toast/toastStore";
  import Loading from "$lib/components/ui/Loading.svelte";
  import { emailTemp, userAuth } from "$lib/stores/app.store";
  import type { User } from "firebase/auth";
  import { onMount } from "svelte";

  // 在这里处理登录回调逻辑

  let isLoading = $state(false);
  let messages: Toast[] = $state([]);

  function isSignInWithEmailLink(emailLink: URL) {
    return true;
  }

  // 验证邮件链接并完成登录
  async function verifyEmailLink() {
    if (!browser) return;

    const currentUrl = new URL(window.location.href);

    // 检查当前 URL 是否为有效的登录链接
    if (isSignInWithEmailLink(currentUrl)) {
      addMessage("检测到邮件登录链接，正在验证...");

      try {
        // 从本地存储获取邮箱
        let emailForSignIn = $emailTemp;

        // 如果没有保存的邮箱，提示用户输入
        if (!emailForSignIn) {
          addMessage("请输入您的邮箱地址以完成登录");
          emailForSignIn = window.prompt("请输入您的邮箱地址以完成登录:") || "";
        }

        if (!emailForSignIn) {
          throw new Error("需要邮箱地址才能完成登录");
        }

        const resp = await fetch(
          "/api/googleapis/?rest=https://identitytoolkit.googleapis.com/v1/accounts:signInWithEmailLink",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email: emailForSignIn.trim(),
              oobCode: currentUrl.searchParams.get("oobCode")?.trim(),
            }),
          },
        );

        const data = await resp.json();

        if (!resp.ok) {
          throw data;
        }

        // 清除本地存储的邮箱
        $emailTemp = "";
        $userAuth = data;

        addMessage(`邮件链接登录成功！欢迎 ${data.email}, ${data}`);

        // 清除 URL 中的查询参数
        window.history.replaceState(
          {},
          document.title,
          window.location.pathname,
        );
      } catch (error: any) {
        console.error("邮件链接登录失败:", error);

        let errorMessage = "邮件链接登录失败";
        switch (error.code) {
          case "auth/invalid-action-code":
            errorMessage = "登录链接无效或已过期";
            break;
          case "auth/invalid-email":
            errorMessage = "邮箱地址无效";
            break;
          case "auth/user-disabled":
            errorMessage = "该用户账户已被禁用";
            break;
          default:
            errorMessage = error.message || JSON.stringify(error);
        }

        error = errorMessage;
        addMessage(`错误: ${errorMessage}`, "error");
      }
    } else {
      addMessage("当前 URL 不是有效的邮件登录链接", "error");
    }
  }

  function addMessage(msg: string, type: ToastType = "success") {
    console.log(type, msg);
    messages = [...messages, { id: messages.length, type: type, message: msg }];
  }

  // 组件挂载时的初始化
  onMount(() => {
    if (browser) {
      isLoading = true;
      verifyEmailLink();
      isLoading = false;
    }
  });
</script>

{#each messages as toast}
  <p class={toast.type === "error" ? "text-red-500" : ""}>{toast.message}</p>
{/each}
<Loading text="登录中..." size="sm"></Loading>
