<script lang="ts">
  import { browser } from "$app/environment";
  import { page } from "$app/state";
  import { addToast } from "$lib/components/toast/toastStore";
  import Button from "$lib/components/ui/Button.svelte";
  import Card from "$lib/components/ui/Card.svelte";
  import Input from "$lib/components/ui/Input.svelte";
  import { fireapp } from "$lib/firebase/firebase";
  import { emailTemp } from "$lib/stores/app.store";
  import { GoogleAuthProvider, signInWithPopup } from "firebase/auth";

  const isNewUser = $derived(page.url.pathname === "/signup");

  let isLoading = $state(false);
  let errorMessage = $state("");

  let email = $state("");

  function addMessage(msg?: any, ...optionalParams: any[]) {
    console.log(msg, ...optionalParams);
  }

  // 发送登录链接到邮箱
  async function handleLoginByEmaillink() {
    addMessage("handleLoginByEmaillink", isLoading, email);
    if (isLoading || !email.trim()) return;

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      errorMessage = "请输入有效的邮箱地址";
      addMessage("邮箱格式无效");
      return;
    }

    isLoading = true;
    errorMessage = "";
    addMessage(`正在发送登录链接到 ${email}...`);

    try {
      // 浏览器语言
      const resp = await fetch(
        "/api/googleapis?rest=https://identitytoolkit.googleapis.com/v1/accounts:sendOobCode",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-Firebase-Locale": navigator.language || "en",
          },
          body: JSON.stringify({
            requestType: "EMAIL_SIGNIN",
            email: email.trim(),
            clientType: "CLIENT_TYPE_WEB",
            continueUrl: window.location.origin,
            canHandleCodeInApp: true,
          }),
        },
      );

      // 保存邮箱到本地存储，以便后续验证
      if (browser) {
        $emailTemp = email.trim();
      }

      if (resp.ok) {
        addToast({ type: "info", message: "登录链接已发送到您的邮箱" });
        addMessage("登录链接已发送到您的邮箱");
        addMessage("请检查您的邮箱并点击链接完成登录");
      } else {
        addToast({ type: "error", message: "发送失败" });
      }
    } catch (error: any) {
      console.error("发送邮件链接时出错:", error);

      let errorMessage = "发送邮件链接失败";
      switch (error.code) {
        case "auth/invalid-email":
          errorMessage = "邮箱地址格式无效";
          break;
        case "auth/user-disabled":
          errorMessage = "该用户账户已被禁用";
          break;
        case "auth/too-many-requests":
          errorMessage = "请求过于频繁，请稍后再试";
          break;
        case "auth/network-request-failed":
          errorMessage = "网络连接失败，请检查网络后重试";
          break;
        default:
          errorMessage = error.message || "未知错误";
      }

      error = errorMessage;
      addMessage(`错误: ${errorMessage}`);
      addToast({ type: "error", message: errorMessage });
    } finally {
      isLoading = false;
    }
  }

  // Google 登录处理
  async function handleLoginByGoogle() {
    isLoading = true;
    errorMessage = "";

    try {
      const fire = fireapp();
      const provider = new GoogleAuthProvider();

      // 添加额外的权限范围
      provider.addScope("profile");
      provider.addScope("email");

      // 设置自定义参数
      provider.setCustomParameters({
        prompt: "select_account",
      });

      const result = await signInWithPopup(fire.auth, provider);

      // 获取 Google Access Token
      const credential = GoogleAuthProvider.credentialFromResult(result);
      const token = credential?.accessToken;

      if (token) addToast({ type: "success", message: "登录成功" });
    } catch (error: any) {
      console.error("登录时出错:", error);

      // 处理不同类型的错误
      errorMessage = "登录失败";
      switch (error.code) {
        case "auth/popup-closed-by-user":
          errorMessage = "登录窗口被用户关闭";
          break;
        case "auth/popup-blocked":
          errorMessage = "登录弹窗被浏览器阻止，请允许弹窗后重试";
          break;
        case "auth/cancelled-popup-request":
          errorMessage = "登录请求被取消";
          break;
        case "auth/network-request-failed":
          errorMessage = "网络连接失败，请检查网络后重试";
          break;
        default:
          errorMessage = error.message || "未知错误";
      }

      error = errorMessage;

      addToast({ type: "error", message: error });
    } finally {
      isLoading = false;
    }
  }
</script>

<div class="mt-20">
  <Card class="w-96 mx-auto space-y-4">
    <h1 class="text-2xl font-bold mb-8">
      🍄 {isNewUser ? "注册" : "登录"}蘑菇 AI 小说平台
    </h1>
    <h3 class="text-lg font-light mt-5 border-t pt-4 border-gray-300">
      使用邮箱登录
    </h3>
    <Input
      class="w-full"
      type="email"
      placeholder="邮箱地址"
      disabled={isLoading}
      bind:value={email}
    />
    <Button
      class="w-full"
      variant="secondary"
      loading={isLoading}
      onclick={handleLoginByEmaillink}>邮箱登录</Button
    >
    <h3 class="text-right font-light mt-5 border-t pt-4 border-gray-300">
      <Button class="w-full" loading={isLoading} onclick={handleLoginByGoogle}
        >使用 Google 登录</Button
      >
    </h3>
    <h3 class="text-right font-light mt-5 border-t pt-4 border-gray-300">
      {#if isNewUser}
        已有账号了, <Button href="/login" size="sm" variant="ghost"
          >直接登录
        </Button>
      {:else}
        还没有账号? <Button href="/signup" size="sm" variant="ghost"
          >注册一个
        </Button>
      {/if}
    </h3>

    <h3 class="text-right font-light mt-5 border-t pt-4 border-gray-300">
      开始测试? <Button
        href={`/__/auth/action?apiKey=AIzaSyB3EAt_QmIvQLmnsgRcEklmKPXwbU05ymw&mode=signIn&oobCode=n2bfvW4wRiyUFHe8MmqturQkvj-rRodujU-wI4A0qwsAAAGYdf5sAw&continueUrl=${page.url.origin}/login/callback&lang=en`}
        target="_blank"
        size="sm"
        variant="ghost"
        >TEST
      </Button>
    </h3>
  </Card>
</div>
